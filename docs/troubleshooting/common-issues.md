# Common Issues and Solutions

This guide covers the most frequently encountered issues when using the Agent Framework and their solutions.

## Troubleshooting Flowchart

```mermaid
flowchart TD
    A[Issue Encountered] --> B{Installation Related?}

    B -->|Yes| C{Python Version?}
    C -->|< 3.10| D[Upgrade Python to 3.10+]
    C -->|>= 3.10| E{Dependencies?}
    E -->|Missing| F[Install requirements.txt]
    E -->|Conflicts| G[Use virtual environment]

    B -->|No| H{Configuration Related?}
    H -->|Yes| I{API Key?}
    I -->|Missing| J[Set AGENT_API_KEY]
    I -->|Invalid| K[Check key format & provider]
    I -->|Valid| L{Config File?}
    L -->|Missing| M[Create config.yaml]
    L -->|Invalid| N[Validate YAML syntax]

    H -->|No| O{Runtime Error?}
    O -->|Yes| P{Error Type?}
    P -->|Rate Limit| Q[Wait & retry or switch provider]
    P -->|Timeout| R[Check network & reduce complexity]
    P -->|Memory| S[Process smaller chunks]
    P -->|Import| T[Check Python path & installation]

    O -->|No| U{Performance Issue?}
    U -->|Yes| V[Enable caching & use faster models]
    U -->|No| W[Check FAQ & create GitHub issue]

    D --> X[Retry Operation]
    F --> X
    G --> X
    J --> X
    K --> X
    M --> X
    N --> X
    Q --> X
    R --> X
    S --> X
    T --> X
    V --> X
    W --> Y[Get Community Help]

    style A fill:#ffebee
    style X fill:#e8f5e8
    style Y fill:#e3f2fd
```

## Installation Issues

### Python Version Compatibility

**Problem**: Error about Python version when installing or running.

**Solution**:
```bash
# Check your Python version
python --version

# Should be 3.10 or higher
# If not, install a newer version or use pyenv
pyenv install 3.11
pyenv local 3.11
```

### Dependency Installation Failures

**Problem**: `pip install` fails with dependency conflicts.

**Solution**:
```bash
# Use a virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Clear pip cache and reinstall
pip cache purge
pip install -r requirements.txt

# Or use uv for better dependency resolution
uv sync
```

### Permission Errors

**Problem**: Permission denied when installing packages.

**Solution**:
```bash
# Don't use sudo with pip, use virtual environment instead
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt

# Or install for user only
pip install --user -r requirements.txt
```

## Configuration Issues

### API Key Problems

**Problem**: "Invalid API key" or authentication errors.

**Solutions**:

1. **Check API key format**:
   ```bash
   # OpenAI keys start with 'sk-'
   # Anthropic keys start with 'sk-ant-'
   # OpenRouter keys start with 'sk-or-'
   echo $AGENT_API_KEY
   ```

2. **Verify environment variables**:
   ```bash
   # Check if variables are set
   env | grep AGENT
   
   # Set them if missing
   export AGENT_API_KEY="your-actual-api-key"
   export AGENT_MODEL="gpt-4o"
   ```

3. **Test API connectivity**:
   ```bash
   # Simple test command
   agent-framework analyze --code "print('hello')" --type complexity
   ```

### Model Configuration Issues

**Problem**: Model not found or unsupported model errors.

**Solution**:
```bash
# Check supported models for your provider
# OpenAI: gpt-4o, gpt-4, gpt-3.5-turbo
# Anthropic: claude-3-5-sonnet-20241022, claude-3-haiku-20240307
# OpenRouter: qwen/qwen3-coder:free, meta-llama/llama-3.1-8b-instruct:free

# Set a known working model
export AGENT_MODEL="gpt-4o"  # For OpenAI
export AGENT_MODEL="claude-3-5-sonnet-20241022"  # For Anthropic
```

### Configuration File Issues

**Problem**: Configuration file not found or invalid format.

**Solution**:
```bash
# Create a minimal config.yaml
cat > config.yaml << EOF
model:
  model: "gpt-4o"
  api_key: "\${AGENT_API_KEY}"
  temperature: 0.7

logging:
  level: "INFO"
EOF

# Test with explicit config
agent-framework --config config.yaml analyze --code "def test(): pass"
```

## CLI Issues

### Command Not Found

**Problem**: `agent-framework: command not found`

**Solutions**:

1. **Use Python module directly**:
   ```bash
   python -m agent_framework.cli.core --help
   ```

2. **Check installation**:
   ```bash
   # Verify the package is installed
   pip list | grep agent
   
   # Reinstall if necessary
   pip install -e .
   ```

3. **Check PATH**:
   ```bash
   # If using virtual environment, make sure it's activated
   which python
   source .venv/bin/activate
   ```

### Import Errors

**Problem**: `ModuleNotFoundError` when running commands.

**Solution**:
```bash
# Make sure you're in the project directory
cd /path/to/agent-framework

# Check Python path
python -c "import sys; print(sys.path)"

# Add src to Python path if needed
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"

# Or use the development installation
pip install -e .
```

### Syntax Errors in Code Analysis

**Problem**: "Invalid Python code syntax" when analyzing valid code.

**Solutions**:

1. **Check file encoding**:
   ```bash
   # Ensure file is UTF-8 encoded
   file -i your_file.py
   ```

2. **Use quotes for code strings**:
   ```bash
   # Escape quotes properly
   agent-framework analyze --code 'def hello(): print("world")'
   ```

3. **Use file input instead**:
   ```bash
   # Avoid shell escaping issues
   agent-framework analyze --file your_file.py
   ```

## Runtime Issues

### Memory Errors

**Problem**: Out of memory errors when processing large files.

**Solutions**:

1. **Process files in smaller chunks**:
   ```bash
   # Analyze specific functions instead of entire files
   agent-framework analyze --code "$(sed -n '1,100p' large_file.py)"
   ```

2. **Increase system memory or use streaming**:
   ```bash
   # Enable memory optimization
   export AGENT_MEMORY_LIMIT="2GB"
   ```

3. **Use basic analysis depth**:
   ```bash
   # Reduce analysis complexity
   agent-framework analyze --file large_file.py --type complexity
   ```

### Timeout Errors

**Problem**: Commands timeout or hang indefinitely.

**Solutions**:

1. **Check network connectivity**:
   ```bash
   # Test API endpoint
   curl -I https://api.openai.com/v1/models
   ```

2. **Reduce request complexity**:
   ```bash
   # Use simpler analysis
   agent-framework analyze --code "def simple(): pass" --type complexity
   ```

3. **Set explicit timeouts**:
   ```bash
   # Add timeout to requests (if supported)
   export AGENT_TIMEOUT=60
   ```

### Rate Limiting

**Problem**: "Rate limit exceeded" errors from API providers.

**Solutions**:

1. **Wait and retry**:
   ```bash
   # Wait a few minutes and try again
   sleep 60
   agent-framework analyze --file your_file.py
   ```

2. **Use different model or provider**:
   ```bash
   # Switch to a less rate-limited model
   export AGENT_MODEL="gpt-3.5-turbo"
   # Or use OpenRouter
   export AGENT_BASE_URL="https://openrouter.ai/api/v1"
   export AGENT_MODEL="qwen/qwen3-coder:free"
   ```

## Multi-Agent Issues

### Agent Registration Failures

**Problem**: Agents not registering or starting properly.

**Solution**:
```bash
# Check agent configuration
agent-framework --verbose analyze --code "def test(): pass"

# Verify multi-agent is enabled in config
cat config.yaml | grep -A 5 multi_agent
```

### MCP Connection Issues

**Problem**: MCP servers not connecting or timing out.

**Solutions**:

1. **Check MCP server installation**:
   ```bash
   # Verify Node.js is installed for MCP servers
   node --version
   npm --version
   
   # Install MCP filesystem server
   npx -y @modelcontextprotocol/server-filesystem
   ```

2. **Test MCP server manually**:
   ```bash
   # Test filesystem server
   npx @modelcontextprotocol/server-filesystem /tmp
   ```

3. **Check server configuration**:
   ```yaml
   # In config.yaml
   mcp:
     servers:
       filesystem:
         command: "npx"
         args: ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/project"]
   ```

## Performance Issues

### Slow Response Times

**Problem**: Commands take too long to execute.

**Solutions**:

1. **Enable caching**:
   ```yaml
   # In config.yaml
   cache:
     enabled: true
     cache_type: "memory"
     ttl_seconds: 3600
   ```

2. **Use faster models**:
   ```bash
   # Switch to faster model
   export AGENT_MODEL="gpt-3.5-turbo"
   ```

3. **Reduce analysis depth**:
   ```bash
   # Use basic analysis
   agent-framework analyze --file code.py --type complexity
   ```

### High Memory Usage

**Problem**: Framework uses too much memory.

**Solutions**:

1. **Limit concurrent operations**:
   ```yaml
   # In config.yaml
   multi_agent:
     max_agents: 2
   ```

2. **Clear cache periodically**:
   ```bash
   # Clear framework cache
   rm -rf ~/.cache/agent-framework/
   ```

## Getting Help

### Enable Debug Mode

```bash
# Get more detailed error information
export AGENT_DEBUG=true
agent-framework --verbose analyze --file problematic_file.py
```

### Check Logs

```bash
# Look for log files
ls -la logs/
cat logs/errors.log
```

### Minimal Reproduction

Create a minimal example that reproduces the issue:

```bash
# Test with simple code
agent-framework analyze --code "def hello(): print('world')" --type complexity

# Test with minimal config
echo 'model: {model: "gpt-3.5-turbo", api_key: "${AGENT_API_KEY}"}' > minimal_config.yaml
agent-framework --config minimal_config.yaml analyze --code "def test(): pass"
```

### Community Support

If you're still having issues:

1. **Check existing issues**: [GitHub Issues](https://github.com/yourusername/agent-framework/issues)
2. **Join Discord**: [Community Discord](https://discord.gg/example)
3. **Create new issue**: Include error messages, configuration, and minimal reproduction steps

## Preventive Measures

### Regular Maintenance

```bash
# Update dependencies regularly
pip install --upgrade -r requirements.txt

# Clear old cache files
find ~/.cache/agent-framework -type f -mtime +7 -delete

# Check for configuration issues
agent-framework --config config.yaml --help
```

### Best Practices

1. **Use virtual environments** for isolation
2. **Keep API keys secure** and rotate them regularly
3. **Monitor usage** to avoid rate limits
4. **Test configuration changes** with simple commands first
5. **Keep backups** of working configurations

## See Also

- [Installation Guide](../getting-started/installation.md)
- [Configuration Guide](../user-guides/configuration.md)
- [FAQ](faq.md)
- [Error Reference](error-reference.md)
