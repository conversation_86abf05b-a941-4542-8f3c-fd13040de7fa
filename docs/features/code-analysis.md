# Code Analysis Features

The Agent Framework provides comprehensive code analysis capabilities to help you understand, evaluate, and improve your code quality.

## Overview

The code analysis system offers multiple types of analysis:

```mermaid
graph LR
    A[Source Code] --> B[Code Parser]
    B --> C{Analysis Type}

    C -->|Complexity| D[Complexity Analyzer]
    C -->|Quality| E[Quality Analyzer]
    C -->|Patterns| F[Pattern Detector]
    C -->|Dependencies| G[Dependency Analyzer]
    C -->|Security| H[Security Scanner]

    D --> I[Cyclomatic Complexity]
    D --> J[Cognitive Complexity]
    D --> K[Maintainability Index]

    E --> L[Code Smells]
    E --> M[Duplication]
    E --> N[Naming Quality]

    F --> O[Design Patterns]
    F --> P[Anti-patterns]

    G --> Q[Import Analysis]
    G --> R[Coupling Metrics]

    H --> S[Vulnerability Detection]
    H --> T[Security Patterns]

    I --> U[Analysis Report]
    J --> U
    K --> U
    L --> U
    M --> U
    N --> U
    O --> U
    P --> U
    Q --> U
    R --> U
    S --> U
    T --> U

    style A fill:#e1f5fe
    style U fill:#e8f5e8
    style C fill:#fff3e0
```

**Analysis Types:**

- **Complexity Analysis**: Measure code complexity and maintainability
- **Quality Metrics**: Assess code quality and identify issues
- **Pattern Detection**: Identify design patterns and anti-patterns
- **Dependency Analysis**: Understand code relationships and coupling
- **Security Analysis**: Detect potential security vulnerabilities

## Analysis Types

### Complexity Analysis

Measures various complexity metrics to help identify code that may be difficult to maintain.

#### Metrics Included

- **Cyclomatic Complexity**: Number of linearly independent paths
- **Cognitive Complexity**: How difficult code is to understand
- **Halstead Metrics**: Program length, vocabulary, and difficulty
- **Maintainability Index**: Overall maintainability score
- **Nesting Depth**: Maximum nesting level in functions

#### Usage

```bash
# Analyze complexity of a file
agent-framework analyze --file mycode.py --type complexity --detailed

# Set complexity threshold
agent-framework analyze --file mycode.py --type complexity --threshold 15

# Analyze specific code snippet
agent-framework analyze --code "def complex_function(): pass" --type complexity
```

#### Example Output

```
Complexity Analysis Results:
├── Cyclomatic Complexity: 8 (Medium)
├── Cognitive Complexity: 12 (High)
├── Maintainability Index: 65 (Good)
├── Max Nesting Depth: 4
└── Functions over threshold: 2

High Complexity Functions:
├── process_data() - Complexity: 15
└── validate_input() - Complexity: 12

Recommendations:
├── Consider breaking down process_data() into smaller functions
└── Reduce nesting in validate_input() using early returns
```

### Quality Analysis

Evaluates overall code quality and identifies potential issues.

#### Quality Metrics

- **Code Smells**: Anti-patterns and problematic code structures
- **Duplication**: Repeated code blocks
- **Naming Conventions**: Variable and function naming quality
- **Documentation Coverage**: Presence of docstrings and comments
- **Type Annotations**: Usage of type hints

#### Usage

```bash
# Comprehensive quality analysis
agent-framework analyze --file mycode.py --type quality --detailed

# Focus on specific quality aspects
agent-framework analyze --file mycode.py --type patterns
```

#### Example Output

```
Quality Analysis Results:
├── Overall Quality Score: 7.2/10
├── Code Smells Found: 3
├── Duplication: 2 blocks (15 lines)
├── Documentation Coverage: 65%
└── Type Annotation Coverage: 80%

Issues Found:
├── Long Method: process_user_data() (45 lines)
├── Magic Numbers: 5 occurrences
├── Missing Docstrings: 3 functions
└── Duplicate Code: lines 45-52 and 78-85

Suggestions:
├── Break down long methods into smaller functions
├── Replace magic numbers with named constants
├── Add docstrings to public functions
└── Extract duplicate code into shared functions
```

### Pattern Detection

Identifies design patterns, anti-patterns, and architectural issues.

#### Detected Patterns

**Design Patterns:**
- Singleton, Factory, Observer, Strategy
- Decorator, Adapter, Command patterns
- MVC, Repository patterns

**Anti-Patterns:**
- God Object, Long Method, Large Class
- Feature Envy, Data Clumps
- Shotgun Surgery, Divergent Change

#### Usage

```bash
# Detect patterns in code
agent-framework analyze --file mycode.py --type patterns --detailed

# Analyze entire project
agent-framework analyze --file src/ --type patterns
```

### Dependency Analysis

Analyzes code dependencies and relationships.

#### Analysis Features

- **Import Analysis**: External and internal dependencies
- **Coupling Metrics**: Afferent and efferent coupling
- **Dependency Graphs**: Visual representation of relationships
- **Circular Dependencies**: Detection of problematic cycles
- **Unused Imports**: Identification of unnecessary imports

#### Usage

```bash
# Analyze dependencies
agent-framework analyze --file mycode.py --type dependencies --detailed

# Check for circular dependencies
agent-framework analyze --file src/ --type dependencies
```

## Advanced Analysis

### Comprehensive Analysis

Runs all analysis types together for a complete code assessment.

```bash
# Complete analysis
agent-framework analyze --file mycode.py --type all --detailed

# With evaluation cycles
agent-framework analyze --file mycode.py --type all --enable-evaluation-cycles
```

### Batch Analysis

Analyze multiple files or entire projects.

```bash
# Analyze entire directory
agent-framework analyze --file src/ --type all --detailed

# Analyze specific file patterns
find src/ -name "*.py" -exec agent-framework analyze --file {} --type complexity \;
```

### Custom Thresholds

Set custom thresholds for different metrics.

```bash
# Custom complexity threshold
agent-framework analyze --file mycode.py --type complexity --threshold 20

# Multiple thresholds in config
```

```yaml
# config.yaml
analysis:
  thresholds:
    cyclomatic_complexity: 15
    cognitive_complexity: 20
    maintainability_index: 60
    max_nesting_depth: 4
```

## Integration with Enhanced Capabilities

### Automatic Evaluation

Enable automatic evaluation cycles for continuous quality assessment.

```python
from agent_framework.core.agent_orchestrator import AdvancedAgentOrchestrator

# Enable automatic evaluation
capabilities = AgentCapabilities(
    enable_automatic_evaluation=True,
    evaluation_on_every_change=True
)

orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)

# Analysis with automatic evaluation
result = await orchestrator.comprehensive_code_analysis(
    code_content=code,
    file_path="example.py",
    analysis_depth="comprehensive"
)
```

### Multi-Agent Analysis

Leverage multiple specialized agents for comprehensive analysis.

```python
# Multi-agent analysis workflow
task = Task(
    name="comprehensive_analysis",
    description="Analyze code quality, complexity, and patterns",
    task_type="workflow",
    parameters={
        "file_path": "src/main.py",
        "analysis_types": ["complexity", "quality", "patterns", "dependencies"]
    }
)

result = await orchestrator.execute_multi_agent_task(task)
```

## Output Formats

### Text Format (Default)

Human-readable analysis results with recommendations.

```bash
agent-framework analyze --file mycode.py --type all
```

### JSON Format

Structured data for programmatic processing.

```bash
agent-framework analyze --file mycode.py --type all --format json
```

### YAML Format

Configuration-friendly structured output.

```bash
agent-framework analyze --file mycode.py --type all --format yaml
```

## Best Practices

### Regular Analysis

1. **Integrate into CI/CD**: Run analysis on every commit
2. **Set Quality Gates**: Fail builds on quality threshold violations
3. **Track Metrics Over Time**: Monitor quality trends
4. **Review Reports**: Regularly review and act on analysis results

### Threshold Configuration

1. **Start Conservative**: Begin with higher thresholds and gradually lower them
2. **Team Consensus**: Agree on thresholds as a team
3. **Context Matters**: Adjust thresholds based on code criticality
4. **Regular Review**: Periodically review and update thresholds

### Actionable Results

1. **Prioritize Issues**: Focus on high-impact, easy-to-fix issues first
2. **Incremental Improvement**: Make small, consistent improvements
3. **Document Decisions**: Record why certain issues are acceptable
4. **Share Knowledge**: Use analysis results for team learning

## Troubleshooting

### Common Issues

1. **Large Files**: Analysis may be slow for very large files
   ```bash
   # Use basic analysis for large files
   agent-framework analyze --file large_file.py --type complexity
   ```

2. **Syntax Errors**: Fix syntax errors before analysis
   ```bash
   # Check syntax first
   python -m py_compile mycode.py
   ```

3. **Memory Issues**: Reduce analysis scope for memory-constrained environments
   ```bash
   # Analyze specific functions instead of entire files
   agent-framework analyze --code "$(sed -n '1,100p' large_file.py)"
   ```

### Performance Tips

1. **Use Caching**: Enable caching for repeated analysis
2. **Parallel Processing**: Analyze multiple files concurrently
3. **Incremental Analysis**: Only analyze changed files
4. **Appropriate Depth**: Use basic analysis for quick checks

## See Also

- [CLI Reference](../user-guides/cli-reference.md) - Complete command reference
- [Enhanced Capabilities](enhanced-capabilities.md) - Advanced analysis features
- [Configuration Guide](../user-guides/configuration.md) - Analysis configuration
- [Examples](../examples/basic-usage.md) - Practical examples
