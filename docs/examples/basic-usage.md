# Basic Usage Examples

This guide provides practical examples of using the Agent Framework for common programming tasks.

## Prerequisites

Before running these examples, make sure you have:

- Installed the Agent Framework ([Installation Guide](../getting-started/installation.md))
- Set up your API key ([Configuration Guide](../user-guides/configuration.md))
- Basic familiarity with the CLI ([CLI Reference](../user-guides/cli-reference.md))

## Example Workflow

```mermaid
graph LR
    A[Write Code] --> B[Analyze Quality]
    B --> C{Issues Found?}
    C -->|Yes| D[Fix Issues]
    C -->|No| E[Generate Tests]
    D --> B
    E --> F[Generate Docs]
    F --> G[Optimize Performance]
    G --> H[Final Review]

    subgraph "Agent Framework Commands"
        B1[agent-framework analyze]
        D1[agent-framework debug --auto-fix]
        E1[agent-framework generate tests]
        F1[agent-framework document]
        G1[agent-framework optimize]
    end

    B -.-> B1
    D -.-> D1
    E -.-> E1
    F -.-> F1
    G -.-> G1

    style A fill:#e1f5fe
    style H fill:#e8f5e8
    style C fill:#fff3e0
```

## Code Analysis Examples

### Analyze Code Quality

```bash
# Analyze a Python file for quality issues
agent-framework analyze --file calculator.py --type quality --detailed
```

**Sample Output:**
```
Quality Analysis Results:
├── Overall Quality Score: 8.1/10
├── Code Smells Found: 2
├── Documentation Coverage: 75%
└── Type Annotation Coverage: 90%

Issues Found:
├── Long Method: calculate_complex() (35 lines)
└── Missing Docstring: helper_function()

Recommendations:
├── Break down calculate_complex() into smaller functions
└── Add docstring to helper_function()
```

### Check Code Complexity

```bash
# Check if code complexity is within acceptable limits
agent-framework analyze --file complex_algorithm.py --type complexity --threshold 10
```

**Sample Output:**
```
Complexity Analysis Results:
├── Cyclomatic Complexity: 12 (High - exceeds threshold)
├── Cognitive Complexity: 15 (High)
└── Functions over threshold: 1

High Complexity Functions:
└── process_data() - Complexity: 12

Suggestions:
└── Consider refactoring process_data() to reduce complexity
```

### Analyze Code Patterns

```bash
# Detect design patterns and anti-patterns
agent-framework analyze --file design_patterns.py --type patterns
```

## Code Generation Examples

### Generate a Function

```bash
# Generate a function with type hints and docstring
agent-framework generate function \
  --name calculate_average \
  --description "Calculate the average of a list of numbers" \
  --parameters "numbers:List[float]" "exclude_zeros:bool:False" \
  --return-type "float"
```

**Generated Code:**
```python
from typing import List

def calculate_average(numbers: List[float], exclude_zeros: bool = False) -> float:
    """Calculate the average of a list of numbers.
    
    Args:
        numbers: List of numbers to calculate average for
        exclude_zeros: Whether to exclude zero values from calculation
        
    Returns:
        The average of the numbers
        
    Raises:
        ValueError: If the list is empty or contains no valid numbers
    """
    if exclude_zeros:
        numbers = [n for n in numbers if n != 0]
    
    if not numbers:
        raise ValueError("Cannot calculate average of empty list")
    
    return sum(numbers) / len(numbers)
```

### Generate a Class

```bash
# Generate a class with methods and attributes
agent-framework generate class \
  --name UserManager \
  --description "Manage user accounts and authentication" \
  --attributes "users:Dict[str, User]" "active_sessions:Set[str]" \
  --methods "add_user:Add a new user to the system" "authenticate:Authenticate user credentials"
```

**Generated Code:**
```python
from typing import Dict, Set
from dataclasses import dataclass

@dataclass
class User:
    username: str
    email: str
    password_hash: str

class UserManager:
    """Manage user accounts and authentication."""
    
    def __init__(self):
        self.users: Dict[str, User] = {}
        self.active_sessions: Set[str] = set()
    
    def add_user(self, username: str, email: str, password: str) -> bool:
        """Add a new user to the system.
        
        Args:
            username: Unique username for the user
            email: User's email address
            password: Plain text password (will be hashed)
            
        Returns:
            True if user was added successfully, False if username exists
        """
        if username in self.users:
            return False
        
        # Hash password (simplified for example)
        password_hash = hash(password)
        self.users[username] = User(username, email, str(password_hash))
        return True
    
    def authenticate(self, username: str, password: str) -> bool:
        """Authenticate user credentials.
        
        Args:
            username: Username to authenticate
            password: Password to verify
            
        Returns:
            True if authentication successful, False otherwise
        """
        if username not in self.users:
            return False
        
        user = self.users[username]
        return user.password_hash == str(hash(password))
```

### Generate Tests

```bash
# Generate test cases for existing code
agent-framework generate tests \
  --file calculator.py \
  --framework pytest \
  --coverage
```

**Generated Tests:**
```python
import pytest
from calculator import Calculator

class TestCalculator:
    """Test cases for Calculator class."""
    
    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.calc = Calculator()
    
    def test_add_positive_numbers(self):
        """Test addition of positive numbers."""
        result = self.calc.add(2, 3)
        assert result == 5
    
    def test_add_negative_numbers(self):
        """Test addition of negative numbers."""
        result = self.calc.add(-2, -3)
        assert result == -5
    
    def test_divide_by_zero(self):
        """Test division by zero raises appropriate exception."""
        with pytest.raises(ZeroDivisionError):
            self.calc.divide(10, 0)
    
    @pytest.mark.parametrize("a,b,expected", [
        (10, 2, 5),
        (15, 3, 5),
        (7, 2, 3.5),
    ])
    def test_divide_parametrized(self, a, b, expected):
        """Test division with multiple parameter sets."""
        result = self.calc.divide(a, b)
        assert result == expected
```

## Code Optimization Examples

### Performance Optimization

```bash
# Optimize code for better performance
agent-framework optimize --file slow_algorithm.py --type performance --show-diff
```

**Before:**
```python
def find_duplicates(items):
    duplicates = []
    for i in range(len(items)):
        for j in range(i + 1, len(items)):
            if items[i] == items[j] and items[i] not in duplicates:
                duplicates.append(items[i])
    return duplicates
```

**After:**
```python
def find_duplicates(items):
    seen = set()
    duplicates = set()
    for item in items:
        if item in seen:
            duplicates.add(item)
        else:
            seen.add(item)
    return list(duplicates)
```

### Memory Optimization

```bash
# Optimize memory usage
agent-framework optimize --file memory_intensive.py --type memory
```

**Optimization Suggestions:**
```
Memory Optimization Results:
├── Large list comprehensions found: 2
├── Unnecessary object creation: 3 instances
└── Memory leaks potential: 1

Recommendations:
├── Use generators instead of list comprehensions for large datasets
├── Reuse objects where possible
└── Explicitly close file handles and database connections
```

## Debugging Examples

### Analyze Error Traceback

```bash
# Analyze an error traceback for debugging insights
agent-framework debug --traceback error.txt --suggest-fixes
```

**Sample error.txt:**
```
Traceback (most recent call last):
  File "main.py", line 15, in <module>
    result = process_data(data)
  File "main.py", line 8, in process_data
    return data[index]
IndexError: list index out of range
```

**Analysis Output:**
```
Error Analysis Results:
├── Error Type: IndexError
├── Root Cause: Array index out of bounds
├── Location: main.py, line 8, in process_data()
└── Confidence: High (95%)

Suggested Fixes:
├── Add bounds checking before accessing array elements
├── Validate input data length before processing
└── Use try-catch block for graceful error handling

Code Suggestions:
```python
def process_data(data):
    if not data or index >= len(data):
        raise ValueError("Invalid data or index")
    return data[index]
```

### Automatic Bug Fixing

```bash
# Attempt to automatically fix detected issues
agent-framework debug --auto-fix --file buggy_code.py --max-iterations 3
```

**Process Output:**
```
Automatic Bug Fixing Session:
├── Iteration 1: Fixed syntax error on line 12
├── Iteration 2: Added missing import statement
├── Iteration 3: Fixed variable scope issue
└── Result: All issues resolved successfully

Fixed Issues:
├── Missing colon in function definition
├── Undefined variable 'json' (added import)
└── Variable 'result' used before assignment
```

## Documentation Generation Examples

### Generate Docstrings

```bash
# Add docstrings to functions and classes
agent-framework document docstrings \
  --file undocumented.py \
  --style google \
  --include-examples
```

**Before:**
```python
def calculate_tax(income, rate, deductions):
    taxable_income = income - deductions
    return taxable_income * rate
```

**After:**
```python
def calculate_tax(income: float, rate: float, deductions: float) -> float:
    """Calculate tax based on income, rate, and deductions.
    
    Args:
        income: Total income amount
        rate: Tax rate as a decimal (e.g., 0.25 for 25%)
        deductions: Total deductible amount
        
    Returns:
        The calculated tax amount
        
    Example:
        >>> calculate_tax(50000, 0.25, 5000)
        11250.0
    """
    taxable_income = income - deductions
    return taxable_income * rate
```

### Generate API Documentation

```bash
# Generate API documentation for a module
agent-framework document api \
  --file mymodule.py \
  --format markdown \
  --with-toc
```

## Enhancement Examples

### Comprehensive Code Enhancement

```bash
# Enhance code with multiple improvement goals
agent-framework enhance \
  --file legacy_code.py \
  --goals quality performance maintainability \
  --comprehensive
```

**Enhancement Process:**
```
Enhancement Session Results:
├── Quality improvements: 8 applied
├── Performance optimizations: 3 applied
├── Maintainability enhancements: 5 applied
└── Overall quality score: 6.2 → 8.7

Applied Changes:
├── Added type hints to all functions
├── Extracted magic numbers to constants
├── Improved variable naming
├── Optimized loop performance
├── Added comprehensive docstrings
└── Refactored complex functions
```

## Interactive Mode Examples

### Natural Language Queries

```bash
# Start interactive mode
agent-framework interactive
```

**Interactive Session:**
```
agent> How can I optimize this function for better performance?
def slow_function(data):
    result = []
    for item in data:
        if item > 0:
            result.append(item * 2)
    return result

🤖 I can help optimize this function! Here are several improvements:

1. Use list comprehension for better performance:
   result = [item * 2 for item in data if item > 0]

2. For very large datasets, consider using a generator:
   return (item * 2 for item in data if item > 0)

3. If you need to process this frequently, consider using NumPy:
   import numpy as np
   data_array = np.array(data)
   return (data_array[data_array > 0] * 2).tolist()

Would you like me to analyze the performance difference between these approaches?

agent> generate a test for this function

🤖 Here's a comprehensive test for your function:

```python
import pytest

def test_slow_function():
    # Test with positive numbers
    assert slow_function([1, 2, 3]) == [2, 4, 6]
    
    # Test with mixed numbers
    assert slow_function([-1, 0, 1, 2]) == [2, 4]
    
    # Test with empty list
    assert slow_function([]) == []
    
    # Test with all negative numbers
    assert slow_function([-1, -2, -3]) == []
```

agent> exit
```

## Batch Processing Examples

### Process Multiple Files

```bash
# Analyze all Python files in a directory
find src/ -name "*.py" -exec agent-framework analyze --file {} --type quality \;

# Generate tests for all modules
for file in src/*.py; do
    agent-framework generate tests --file "$file" --framework pytest
done

# Enhance all files in a project
agent-framework enhance --file src/ --goals quality --comprehensive
```

## Integration Examples

### CI/CD Integration

```bash
#!/bin/bash
# ci-quality-check.sh

# Run quality analysis
agent-framework analyze --file src/ --type all --format json > quality_report.json

# Check if quality meets threshold
quality_score=$(jq '.overall_quality_score' quality_report.json)
if (( $(echo "$quality_score < 7.0" | bc -l) )); then
    echo "Quality score $quality_score below threshold 7.0"
    exit 1
fi

echo "Quality check passed with score $quality_score"
```

### Pre-commit Hook

```bash
#!/bin/bash
# .git/hooks/pre-commit

# Analyze staged Python files
for file in $(git diff --cached --name-only --diff-filter=ACM | grep '\.py$'); do
    echo "Analyzing $file..."
    agent-framework analyze --file "$file" --type complexity --threshold 15
    if [ $? -ne 0 ]; then
        echo "Complexity check failed for $file"
        exit 1
    fi
done

echo "All files passed complexity check"
```

## Next Steps

After trying these basic examples:

1. Explore [Advanced Scenarios](advanced-scenarios.md) for complex use cases
2. Learn about [Multi-Agent Setup](../user-guides/multi-agent-setup.md) for coordinated workflows
3. Check [Enhanced Capabilities](../features/enhanced-capabilities.md) for advanced features
4. Review [Configuration Guide](../user-guides/configuration.md) for customization options

## See Also

- [CLI Reference](../user-guides/cli-reference.md) - Complete command documentation
- [Quick Start Guide](../getting-started/quick-start.md) - Getting started tutorial
- [Troubleshooting](../troubleshooting/common-issues.md) - Common issues and solutions
